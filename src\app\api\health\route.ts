import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongo';

interface HealthData {
  status: string;
  timestamp: string;
  environment: string;
  version: string;
  uptime: number;
  system: {
    platform: NodeJS.Platform;
    arch: NodeJS.Architecture;
    nodeVersion: string;
    pid: number;
  };
  memory: {
    used: number;
    total: number;
    external: number;
  };
  performance: {
    responseTime: number;
  };
  checks: {
    api: string;
    database: string;
    redis: string;
    filesystem: string;
    rateLimiter?: string;
  };
  metrics: {
    requestCount: number;
    errorRate: number;
    averageResponseTime: number;
    databaseResponseTime?: string;
    rateLimiterResponseTime?: string;
  };
  warnings?: string[];
}

// Health check endpoint for VPS monitoring
export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Basic health check response
    const healthData: HealthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
      },
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
      },
      performance: {
        responseTime: 0,
      },
      checks: {
        api: 'healthy',
        database: 'checking',
        redis: 'checking',
        filesystem: 'checking',
      },
      metrics: {
        requestCount: 0,
        errorRate: 0,
        averageResponseTime: 0,
      }
    };

    // Database connectivity check
    try {
      await connectToDatabase();
      healthData.checks.database = 'healthy';
    } catch (error) {
      healthData.checks.database = 'unhealthy';
      healthData.status = 'degraded';
      console.error('Database health check failed:', error);
    }

    // Rate limiter check (in-memory store)
    try {
      // Test the rate limiter functionality
      const testKey = `health-check-${Date.now()}`;
      const { rateLimiter } = await import('@/lib/rateLimiter');
      const result = await rateLimiter.checkLimit(testKey, 'general');

      if (result.success) {
        healthData.checks.rateLimiter = 'healthy';
      } else {
        healthData.checks.rateLimiter = 'degraded';
      }
    } catch (error) {
      healthData.checks.rateLimiter = 'unhealthy';
      healthData.status = 'degraded';
      console.error('Rate limiter health check failed:', error);
    }

    // Calculate response time
    const responseTime = Date.now() - startTime;
    healthData.responseTime = `${responseTime}ms`;

    // Performance check - fail if response time > 5 seconds
    if (responseTime > 5000) {
      healthData.status = 'degraded';
      healthData.warning = 'High response time detected';
    }

    // Return appropriate status code
    const statusCode = healthData.status === 'healthy' ? 200 : 503;
    
    return NextResponse.json(healthData, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      }
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Internal server error',
      responseTime: `${Date.now() - startTime}ms`,
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      }
    });
  }
}

// Detailed health check for admin monitoring
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Check if request is from admin or monitoring system
    const authHeader = request.headers.get('authorization');
    const monitoringKey = process.env.MONITORING_API_KEY;
    
    if (!authHeader || !monitoringKey || authHeader !== `Bearer ${monitoringKey}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Detailed system information
    const detailedHealth: HealthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
      },
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
      },
      performance: {
        responseTime: 0,
      },
      checks: {
        api: 'healthy',
        database: 'checking',
        redis: 'checking',
        filesystem: 'checking',
      },
      metrics: {
        requestCount: 0, // This would be tracked in a real implementation
        errorRate: 0,
        averageResponseTime: 0,
      }
    };

    // Database detailed check
    try {
      const dbStart = Date.now();
      await connectToDatabase();
      const dbTime = Date.now() - dbStart;
      detailedHealth.checks.database = 'healthy';
      detailedHealth.metrics.databaseResponseTime = `${dbTime}ms`;
    } catch (error) {
      detailedHealth.checks.database = 'unhealthy';
      detailedHealth.status = 'degraded';
    }

    // Rate limiter detailed check
    try {
      const rateLimiterStart = Date.now();
      const testKey = `detailed-health-check-${Date.now()}`;
      const { rateLimiter } = await import('@/lib/rateLimiter');
      const result = await rateLimiter.checkLimit(testKey, 'general');
      const rateLimiterTime = Date.now() - rateLimiterStart;

      if (result.success) {
        detailedHealth.checks.rateLimiter = 'healthy';
        detailedHealth.metrics.rateLimiterResponseTime = `${rateLimiterTime}ms`;
      } else {
        detailedHealth.checks.rateLimiter = 'degraded';
        detailedHealth.status = 'degraded';
      }
    } catch (error) {
      detailedHealth.checks.rateLimiter = 'unhealthy';
      detailedHealth.status = 'degraded';
    }

    // Filesystem check
    try {
      const fs = require('fs').promises;
      await fs.access('./package.json');
      detailedHealth.checks.filesystem = 'healthy';
    } catch (error) {
      detailedHealth.checks.filesystem = 'unhealthy';
      detailedHealth.status = 'degraded';
    }

    // Calculate total response time
    const responseTime = Date.now() - startTime;
    detailedHealth.performance.responseTime = responseTime;

    // Performance warnings
    if (responseTime > 3000) {
      detailedHealth.status = 'degraded';
      detailedHealth.warnings = detailedHealth.warnings || [];
      detailedHealth.warnings.push('High response time detected');
    }

    if (detailedHealth.memory.used > 1000) { // > 1GB
      detailedHealth.status = 'degraded';
      detailedHealth.warnings = detailedHealth.warnings || [];
      detailedHealth.warnings.push('High memory usage detected');
    }

    const statusCode = detailedHealth.status === 'healthy' ? 200 : 503;
    
    return NextResponse.json(detailedHealth, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      }
    });

  } catch (error) {
    console.error('Detailed health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Internal server error',
      responseTime: `${Date.now() - startTime}ms`,
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      }
    });
  }
}
